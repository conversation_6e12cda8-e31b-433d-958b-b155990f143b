#property strict

#include "TradingPipeline.mqh"
#include "TradingPipelineDriver.mqh"

class MainPipeline : public TradingPipeline
{
public:
    MainPipeline(string name = "",
                      string type = "MainPipeline",
                      ENUM_TRADING_STAGE stage = INIT_START,
                      ITradingPipelineDriver* driver = NULL)
        : TradingPipeline(name, type, stage, driver!=NULL?driver:TradingPipelineDriver::GetInstance())
    {
        driver.GetRegistry().Register(&this);
    }

protected:
    // 主程序 - 子類必須實現
    virtual void Main() = 0;
};